// ATLS Cases Landing Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all features
    initPreloader();
    initPurchasePopup();
    initSmoothScrolling();
    initGallerySwiper();
    initEmailSignup();
    initScrollEffects();
    initAccessibility();
    initVideoEnhancements();
});

// Preloader
function initPreloader() {
    const preloader = document.getElementById('preloader');
    
    window.addEventListener('load', function() {
        setTimeout(() => {
            preloader.style.opacity = '0';
            setTimeout(() => {
                preloader.style.display = 'none';
            }, 500);
        }, 1000);
    });
}

// Purchase Notification Popup
function initPurchasePopup() {
    const popup = document.getElementById('purchase-popup');
    const popupText = document.getElementById('popup-text');
    const closeBtn = popup.querySelector('.popup-close');
    
    const buyers = [
        // North America
        { name: 'Dr. <PERSON>', country: 'United States', flag: '🇺🇸' },
        { name: 'Dr. <PERSON>', country: 'Canada', flag: '🇨🇦' },
        { name: 'Dr. <PERSON> Silva', country: 'Mexico', flag: '🇲🇽' },

        // South America
        { name: 'Carlos Rodriguez', country: 'Brazil', flag: '🇧🇷' },
        { name: 'Dr. Ana Gutierrez', country: 'Argentina', flag: '🇦🇷' },
        { name: 'Dr. Luis Morales', country: 'Colombia', flag: '🇨🇴' },
        { name: 'Dr. Carmen Vega', country: 'Chile', flag: '🇨🇱' },

        // Europe
        { name: 'Dr. Hans Mueller', country: 'Germany', flag: '🇩🇪' },
        { name: 'Sophie Martin', country: 'France', flag: '🇫🇷' },
        { name: 'Emma Thompson', country: 'United Kingdom', flag: '🇬🇧' },
        { name: 'Maria Gonzalez', country: 'Spain', flag: '🇪🇸' },
        { name: 'Dr. Marco Rossi', country: 'Italy', flag: '🇮🇹' },
        { name: 'Anna Kowalski', country: 'Poland', flag: '🇵🇱' },
        { name: 'Dr. Lisa Anderson', country: 'Sweden', flag: '🇸🇪' },
        { name: 'Dr. Erik Larsen', country: 'Norway', flag: '🇳🇴' },
        { name: 'Dr. Petra Novak', country: 'Czech Republic', flag: '🇨🇿' },
        { name: 'Dr. Dimitri Petrov', country: 'Russia', flag: '🇷🇺' },

        // Asia
        { name: 'Dr. Ahmed Hassan', country: 'Egypt', flag: '🇪🇬' },
        { name: 'Priya Sharma', country: 'India', flag: '🇮🇳' },
        { name: 'Dr. David Kim', country: 'South Korea', flag: '🇰🇷' },
        { name: 'Yuki Tanaka', country: 'Japan', flag: '🇯🇵' },
        { name: 'Dr. Li Wei', country: 'China', flag: '🇨🇳' },
        { name: 'Dr. Fatima Al-Zahra', country: 'Saudi Arabia', flag: '🇸🇦' },
        { name: 'Dr. Ravi Patel', country: 'India', flag: '🇮🇳' },
        { name: 'Dr. Mei Lin', country: 'Singapore', flag: '🇸🇬' },
        { name: 'Dr. Hassan Ali', country: 'UAE', flag: '🇦🇪' },
        { name: 'Dr. Kenji Nakamura', country: 'Japan', flag: '🇯🇵' },
        { name: 'Dr. Arjun Gupta', country: 'India', flag: '🇮🇳' },

        // Africa
        { name: 'Dr. Kwame Asante', country: 'Ghana', flag: '🇬🇭' },
        { name: 'Dr. Amina Kone', country: 'Ivory Coast', flag: '🇨🇮' },
        { name: 'Dr. Thabo Mthembu', country: 'South Africa', flag: '🇿🇦' },
        { name: 'Dr. Fatou Diallo', country: 'Senegal', flag: '🇸🇳' },
        { name: 'Dr. Omar Benali', country: 'Morocco', flag: '🇲🇦' },

        // Oceania
        { name: 'Dr. Michael Brown', country: 'Australia', flag: '🇦🇺' },
        { name: 'Dr. Sarah Wilson', country: 'New Zealand', flag: '🇳🇿' },

        // Middle East
        { name: 'Dr. Leila Hosseini', country: 'Iran', flag: '🇮🇷' },
        { name: 'Dr. Yusuf Kaya', country: 'Turkey', flag: '🇹🇷' },
        { name: 'Dr. Nour Khalil', country: 'Lebanon', flag: '🇱🇧' },
        { name: 'Dr. Samir Qasemi', country: 'Jordan', flag: '🇯🇴' },

        // Additional European Countries
        { name: 'Dr. Olga Petersen', country: 'Denmark', flag: '🇩🇰' },
        { name: 'Dr. Mikael Virtanen', country: 'Finland', flag: '🇫🇮' },
        { name: 'Dr. Ingrid Johansson', country: 'Sweden', flag: '🇸🇪' },
        { name: 'Dr. Klaus Weber', country: 'Austria', flag: '🇦🇹' },
        { name: 'Dr. Francois Dubois', country: 'Belgium', flag: '🇧🇪' },
        { name: 'Dr. Jan van der Berg', country: 'Netherlands', flag: '🇳🇱' }
    ];
    
    function showRandomPurchase() {
        const randomBuyer = buyers[Math.floor(Math.random() * buyers.length)];
        popupText.innerHTML = `${randomBuyer.flag} <strong>${randomBuyer.name}</strong> from ${randomBuyer.country} just bought a copy!`;

        popup.classList.add('show');

        setTimeout(() => {
            popup.classList.remove('show');
        }, 6000);
    }
    
    // Show first popup after 10 seconds
    setTimeout(showRandomPurchase, 10000);
    
    // Show random popups every 30-60 seconds
    setInterval(() => {
        const randomDelay = Math.random() * 30000 + 30000; // 30-60 seconds
        setTimeout(showRandomPurchase, randomDelay);
    }, 60000);
    
    // Close popup functionality
    closeBtn.addEventListener('click', function() {
        popup.classList.remove('show');
    });
    
    popup.addEventListener('click', function(e) {
        if (e.target === popup) {
            popup.classList.remove('show');
        }
    });
}

// Smooth Scrolling
function initSmoothScrolling() {
    const navLinks = document.querySelectorAll('a[href^="#"]');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                const headerHeight = document.querySelector('.sticky-header').offsetHeight;
                const targetPosition = targetSection.offsetTop - headerHeight - 20;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// Professional Gallery Swiper
function initGallerySwiper() {
    if (typeof Swiper !== 'undefined') {
        // Thumbnail Gallery
        const thumbnailSwiper = new Swiper('.thumbnail-gallery-swiper', {
            spaceBetween: 10,
            slidesPerView: 4,
            freeMode: true,
            watchSlidesProgress: true,
            breakpoints: {
                640: {
                    slidesPerView: 6,
                },
                768: {
                    slidesPerView: 8,
                },
                1024: {
                    slidesPerView: 10,
                },
            },
        });

        // Main Gallery
        const mainSwiper = new Swiper('.main-gallery-swiper', {
            spaceBetween: 10,
            loop: true,
            autoplay: {
                delay: 4000,
                disableOnInteraction: false,
                pauseOnMouseEnter: true,
            },
            pagination: {
                el: '.gallery-pagination',
                clickable: true,
                dynamicBullets: true,
            },
            navigation: {
                nextEl: '.gallery-next',
                prevEl: '.gallery-prev',
            },
            thumbs: {
                swiper: thumbnailSwiper,
            },
            effect: 'fade',
            fadeEffect: {
                crossFade: true,
            },
            on: {
                slideChange: function () {
                    // Add animation to gallery info
                    const activeSlide = this.slides[this.activeIndex];
                    const galleryInfo = activeSlide.querySelector('.gallery-info');
                    if (galleryInfo) {
                        galleryInfo.style.animation = 'fadeInUp 0.6s ease';
                    }
                },
            },
        });

        // Add keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') {
                mainSwiper.slidePrev();
            } else if (e.key === 'ArrowRight') {
                mainSwiper.slideNext();
            }
        });

        // Add fullscreen functionality
        const galleryImages = document.querySelectorAll('.gallery-image');
        galleryImages.forEach(img => {
            img.addEventListener('click', function() {
                openImageModal(this.src, this.alt);
            });
        });
    }
}

// Image Modal for Fullscreen View
function openImageModal(src, alt) {
    const modal = document.createElement('div');
    modal.className = 'image-modal';
    modal.innerHTML = `
        <div class="modal-backdrop">
            <div class="modal-content">
                <button class="modal-close">&times;</button>
                <img src="${src}" alt="${alt}" class="modal-image">
                <div class="modal-caption">${alt}</div>
            </div>
        </div>
    `;

    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        opacity: 0;
        transition: opacity 0.3s ease;
    `;

    const modalContent = modal.querySelector('.modal-content');
    modalContent.style.cssText = `
        position: relative;
        max-width: 90%;
        max-height: 90%;
        text-align: center;
    `;

    const modalImage = modal.querySelector('.modal-image');
    modalImage.style.cssText = `
        max-width: 100%;
        max-height: 80vh;
        object-fit: contain;
        border-radius: 8px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    `;

    const modalClose = modal.querySelector('.modal-close');
    modalClose.style.cssText = `
        position: absolute;
        top: -40px;
        right: 0;
        background: none;
        border: none;
        color: white;
        font-size: 30px;
        cursor: pointer;
        z-index: 10001;
    `;

    const modalCaption = modal.querySelector('.modal-caption');
    modalCaption.style.cssText = `
        color: white;
        margin-top: 15px;
        font-size: 1.1rem;
    `;

    document.body.appendChild(modal);

    setTimeout(() => {
        modal.style.opacity = '1';
    }, 10);

    // Close modal functionality
    function closeModal() {
        modal.style.opacity = '0';
        setTimeout(() => {
            document.body.removeChild(modal);
        }, 300);
    }

    modalClose.addEventListener('click', closeModal);
    modal.addEventListener('click', function(e) {
        if (e.target === modal || e.target.className === 'modal-backdrop') {
            closeModal();
        }
    });

    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeModal();
        }
    });
}

// Video Enhancements
function initVideoEnhancements() {
    // Add loading states for video iframes
    const videoIframes = document.querySelectorAll('.responsive-iframe');

    videoIframes.forEach(iframe => {
        const wrapper = iframe.closest('.video-wrapper');
        const loadingIndicator = document.createElement('div');
        loadingIndicator.className = 'video-loading';
        loadingIndicator.innerHTML = `
            <div class="loading-spinner">
                <i class="fas fa-circle-notch fa-spin"></i>
                <p>Loading video...</p>
            </div>
        `;

        loadingIndicator.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            z-index: 10;
            transition: opacity 0.3s ease;
        `;

        const spinner = loadingIndicator.querySelector('.loading-spinner');
        spinner.style.cssText = `
            text-align: center;
        `;

        const spinnerIcon = loadingIndicator.querySelector('i');
        spinnerIcon.style.cssText = `
            font-size: 2rem;
            margin-bottom: 10px;
            display: block;
        `;

        wrapper.appendChild(loadingIndicator);

        // Remove loading indicator when iframe loads
        iframe.addEventListener('load', function() {
            setTimeout(() => {
                loadingIndicator.style.opacity = '0';
                setTimeout(() => {
                    if (loadingIndicator.parentNode) {
                        loadingIndicator.parentNode.removeChild(loadingIndicator);
                    }
                }, 300);
            }, 1000);
        });
    });

    // Add video analytics tracking
    const videoCards = document.querySelectorAll('.video-card');

    videoCards.forEach((card, index) => {
        card.addEventListener('click', function(e) {
            // Track video interaction
            console.log(`Video ${index + 1} interaction tracked`);

            // Add visual feedback
            const ripple = document.createElement('div');
            ripple.className = 'click-ripple';
            ripple.style.cssText = `
                position: absolute;
                border-radius: 50%;
                background: rgba(52, 152, 219, 0.3);
                transform: scale(0);
                animation: ripple 0.6s linear;
                pointer-events: none;
            `;

            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = (e.clientX - rect.left - size / 2) + 'px';
            ripple.style.top = (e.clientY - rect.top - size / 2) + 'px';

            this.style.position = 'relative';
            this.appendChild(ripple);

            setTimeout(() => {
                if (ripple.parentNode) {
                    ripple.parentNode.removeChild(ripple);
                }
            }, 600);
        });
    });

    // Add CSS for ripple animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);

    // Lazy load videos for better performance
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '50px 0px'
    };

    const videoObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const iframe = entry.target;
                if (iframe.dataset.src) {
                    iframe.src = iframe.dataset.src;
                    iframe.removeAttribute('data-src');
                    videoObserver.unobserve(iframe);
                }
            }
        });
    }, observerOptions);

    // Observe video iframes for lazy loading
    videoIframes.forEach(iframe => {
        if (iframe.src) {
            iframe.dataset.src = iframe.src;
            iframe.src = '';
            videoObserver.observe(iframe);
        }
    });
}

// Email Signup
function initEmailSignup() {
    const form = document.getElementById('emailSignupForm');
    
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = this.querySelector('input[type="email"]').value;
            
            if (validateEmail(email)) {
                // Simulate successful signup
                showNotification('Thank you for subscribing! Check your email for the free trauma assessment checklist.', 'success');
                this.reset();
            } else {
                showNotification('Please enter a valid email address.', 'error');
            }
        });
    }
}

// Email validation
function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

// Show notification
function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: ${type === 'success' ? '#27ae60' : '#e74c3c'};
        color: white;
        padding: 15px 20px;
        border-radius: 12px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        z-index: 1001;
        transform: translateX(400px);
        transition: transform 0.3s ease;
        max-width: 300px;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    setTimeout(() => {
        notification.style.transform = 'translateX(400px)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 4000);
}

// Scroll Effects
function initScrollEffects() {
    const header = document.querySelector('.sticky-header');
    
    window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
            header.style.background = 'rgba(255, 255, 255, 0.98)';
            header.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.15)';
        } else {
            header.style.background = 'rgba(255, 255, 255, 0.95)';
            header.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
        }
    });
    
    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    const animatedElements = document.querySelectorAll('.feature-card, .testimonial-card, .format-card, .content-item');
    animatedElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
}

// Accessibility Features
function initAccessibility() {
    // Keyboard navigation for custom elements
    const interactiveElements = document.querySelectorAll('.btn, .nav-link, .social-btn');
    
    interactiveElements.forEach(element => {
        element.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.click();
            }
        });
    });
    
    // Focus management for modals and popups
    const popup = document.getElementById('purchase-popup');
    const closeBtn = popup.querySelector('.popup-close');
    
    popup.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            popup.classList.remove('show');
        }
    });
    
    // Skip to main content link
    const skipLink = document.createElement('a');
    skipLink.href = '#main-content';
    skipLink.textContent = 'Skip to main content';
    skipLink.className = 'skip-link';
    skipLink.style.cssText = `
        position: absolute;
        top: -40px;
        left: 6px;
        background: #000;
        color: #fff;
        padding: 8px;
        text-decoration: none;
        z-index: 10000;
        border-radius: 4px;
    `;
    
    skipLink.addEventListener('focus', function() {
        this.style.top = '6px';
    });
    
    skipLink.addEventListener('blur', function() {
        this.style.top = '-40px';
    });
    
    document.body.insertBefore(skipLink, document.body.firstChild);
}

// Social Sharing Functions
function shareOnTwitter() {
    const text = 'Master ATLS Principles with 100 Realistic Trauma Scenarios by Dr. Atef Ahmed - Essential case-based review for emergency medicine professionals! #ATLS #TraumaCare #EmergencyMedicine #MedicalEducation';
    const url = window.location.href;
    window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`, '_blank');
}

function shareOnFacebook() {
    const url = window.location.href;
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`, '_blank');
}

function shareOnLinkedIn() {
    const title = 'Advanced Trauma Life Support (ATLS) Cases Scenarios by Dr. Atef Ahmed';
    const summary = 'Master ATLS Principles with 100 Realistic Trauma Scenarios - Essential case-based review for emergency medicine professionals';
    const url = window.location.href;
    window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}&title=${encodeURIComponent(title)}&summary=${encodeURIComponent(summary)}`, '_blank');
}

function shareOnWhatsApp() {
    const text = 'Check out this amazing ATLS Cases Scenarios book by Dr. Atef Ahmed - perfect for mastering trauma care! 🏥📚';
    const url = window.location.href;
    window.open(`https://wa.me/?text=${encodeURIComponent(text + ' ' + url)}`, '_blank');
}

// Performance Optimization
function optimizeImages() {
    const images = document.querySelectorAll('img');
    
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    if (img.dataset.src) {
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                }
            });
        });
        
        images.forEach(img => {
            if (img.dataset.src) {
                imageObserver.observe(img);
            }
        });
    }
}

// Initialize image optimization
optimizeImages();

// Error handling
window.addEventListener('error', function(e) {
    console.error('JavaScript error:', e.error);
});

// Service Worker registration for PWA capabilities
if ('serviceWorker' in navigator) {
    window.addEventListener('load', function() {
        navigator.serviceWorker.register('/sw.js')
            .then(function(registration) {
                console.log('ServiceWorker registration successful');
            })
            .catch(function(err) {
                console.log('ServiceWorker registration failed');
            });
    });
}
