/* Custom CSS for ATLS Cases Landing Page */

:root {
    --primary-color: #8B4513;
    --secondary-color: #A0522D;
    --accent-color: #D2691E;
    --success-color: #CD853F;
    --warning-color: #DEB887;
    --light-bg: #8B4513;
    --dark-bg: #654321;
    --text-dark: #ffffff;
    --text-light: #ffffff;
    --white: #8B4513;
    --gray-50: #8B4513;
    --gray-100: #A0522D;
    --gray-200: #D2691E;
    --gray-800: #654321;
    --gray-900: #5D4037;
    --brown-light: #D2691E;
    --brown-medium: #A0522D;
    --brown-dark: #8B4513;
    --brown-darker: #654321;
    --brown-darkest: #5D4037;
    --border-radius: 16px;
    --box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.7;
    color: #ffffff !important;
    overflow-x: hidden;
    background: var(--brown-dark) !important;
    font-weight: 700 !important;
}

/* Preloader */
#preloader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--white);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
}

.preloader-content {
    text-align: center;
}

.medical-cross {
    width: 60px;
    height: 60px;
    position: relative;
    margin: 0 auto 20px;
    animation: pulse 2s infinite;
}

.cross-vertical, .cross-horizontal {
    background: var(--accent-color);
    position: absolute;
    border-radius: 3px;
}

.cross-vertical {
    width: 8px;
    height: 60px;
    left: 50%;
    transform: translateX(-50%);
}

.cross-horizontal {
    width: 60px;
    height: 8px;
    top: 50%;
    transform: translateY(-50%);
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Purchase Popup */
.purchase-popup {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--success-color);
    color: white;
    padding: 15px 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    z-index: 1000;
    transform: translateX(400px);
    transition: var(--transition);
    max-width: 300px;
}

.purchase-popup.show {
    transform: translateX(0);
}

.popup-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.popup-close {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    margin-left: auto;
}

/* Header */
.sticky-header {
    position: fixed;
    top: 0;
    width: 100%;
    background: var(--brown-darkest) !important;
    backdrop-filter: blur(20px);
    z-index: 1000;
    transition: var(--transition);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
    border-bottom: 2px solid var(--brown-light);
}

.navbar-brand {
    font-weight: 900;
    font-size: 1.5rem;
    color: #ffffff !important;
    text-decoration: none;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.navbar-brand i {
    color: var(--brown-light);
    margin-right: 8px;
}

.nav-link {
    font-weight: 700;
    color: #ffffff !important;
    transition: var(--transition);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.nav-link:hover {
    color: var(--brown-light) !important;
}

/* Hero Section */
.hero-section {
    padding: 140px 0 100px;
    background: linear-gradient(135deg, var(--brown-darkest) 0%, var(--brown-darker) 50%, var(--brown-dark) 100%);
    position: relative;
    overflow: hidden;
    color: #ffffff !important;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="medical-grid" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/><path d="M 8 10 L 12 10 M 10 8 L 10 12" stroke="rgba(255,255,255,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23medical-grid)"/></svg>');
    opacity: 0.4;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.badge-container {
    margin-bottom: 20px;
}

.badge {
    font-size: 0.9rem;
    padding: 8px 16px;
    margin-right: 10px;
    border-radius: 25px;
}

.hero-title {
    font-family: 'Playfair Display', serif;
    font-size: 4rem;
    font-weight: 800;
    color: white;
    margin-bottom: 24px;
    line-height: 1.1;
    text-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
    font-size: 1.75rem;
    color: #93c5fd;
    margin-bottom: 24px;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.hero-description {
    font-size: 1.25rem;
    color: #e2e8f0;
    margin-bottom: 40px;
    line-height: 1.8;
    font-weight: 400;
}

.hero-features {
    margin-bottom: 30px;
}

.feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.feature-item {
    color: #cbd5e0;
}

.feature-item i {
    color: var(--success-color);
    margin-right: 12px;
    font-size: 1.2rem;
}

.feature-item span {
    font-weight: 500;
}

.cta-buttons {
    margin-bottom: 40px;
}

.cta-buttons .btn {
    margin-right: 15px;
    margin-bottom: 10px;
    padding: 12px 30px;
    font-weight: 600;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.btn-primary {
    background: linear-gradient(135deg, var(--secondary-color), #3182ce);
    border: none;
    box-shadow: 0 10px 15px -3px rgba(66, 153, 225, 0.4), 0 4px 6px -2px rgba(66, 153, 225, 0.05);
    font-weight: 600;
    letter-spacing: 0.025em;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #3182ce, #2c5282);
    transform: translateY(-3px);
    box-shadow: 0 20px 25px -5px rgba(66, 153, 225, 0.4), 0 10px 10px -5px rgba(66, 153, 225, 0.1);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #38a169);
    border: none;
    box-shadow: 0 10px 15px -3px rgba(72, 187, 120, 0.4), 0 4px 6px -2px rgba(72, 187, 120, 0.05);
    font-weight: 600;
    letter-spacing: 0.025em;
}

.btn-success:hover {
    background: linear-gradient(135deg, #38a169, #2f855a);
    transform: translateY(-3px);
    box-shadow: 0 20px 25px -5px rgba(72, 187, 120, 0.4), 0 10px 10px -5px rgba(72, 187, 120, 0.1);
}

.btn-outline-primary {
    border: 2px solid #93c5fd;
    color: #93c5fd;
    background: rgba(147, 197, 253, 0.1);
    backdrop-filter: blur(10px);
    font-weight: 600;
}

.btn-outline-primary:hover {
    background: #93c5fd;
    color: var(--gray-900);
    transform: translateY(-3px);
    box-shadow: 0 10px 15px -3px rgba(147, 197, 253, 0.4);
}

.social-proof {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding-top: 24px;
}

.social-proof .rating span,
.social-proof .buyers-count {
    color: #e2e8f0;
}

.rating {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.stars {
    color: var(--warning-color);
    margin-right: 10px;
}

.buyers-count {
    color: var(--text-light);
    font-size: 0.9rem;
}

/* Hero Image */
.hero-image {
    position: relative;
    text-align: center;
}

.book-showcase {
    position: relative;
    display: inline-block;
}

.book-cover {
    max-width: 100%;
    height: auto;
    border-radius: var(--border-radius);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    transition: var(--transition);
}

.book-cover:hover {
    transform: scale(1.05) rotateY(5deg);
}

.floating-elements {
    position: absolute;
    top: 20px;
    right: -20px;
}

.floating-badge {
    background: var(--accent-color);
    color: white;
    padding: 10px 15px;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    font-weight: 600;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Section Styling */
.section-title {
    font-family: 'Playfair Display', serif;
    font-size: 3rem;
    font-weight: 800;
    color: #111827 !important;
    margin-bottom: 20px;
    line-height: 1.2;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Override for testimonials section specifically */
section#testimonials .section-title,
section.testimonials-section .section-title {
    color: #fbbf24 !important;
    text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.8);
    font-weight: 900;
}

.section-subtitle {
    font-size: 1.375rem;
    color: #374151 !important;
    margin-bottom: 40px;
    font-weight: 600;
    line-height: 1.6;
}

/* About Section */
.about-section {
    padding: 100px 0;
    background: var(--brown-medium) !important;
    position: relative;
}

.about-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--brown-medium) !important;
}

.feature-card {
    text-align: center;
    padding: 48px 32px;
    border-radius: var(--border-radius);
    background: var(--brown-dark) !important;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    height: 100%;
    border: 2px solid var(--brown-light) !important;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--secondary-color), var(--accent-color));
}

.feature-card:hover {
    transform: translateY(-12px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    border-color: var(--secondary-color);
}

.feature-icon {
    width: 96px;
    height: 96px;
    background: linear-gradient(135deg, var(--secondary-color), #3182ce);
    border-radius: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
    color: white;
    font-size: 2.5rem;
    box-shadow: 0 10px 15px -3px rgba(66, 153, 225, 0.4);
}

.feature-card h3 {
    font-size: 1.75rem;
    font-weight: 800;
    color: #111827 !important;
    margin-bottom: 16px;
    line-height: 1.3;
}

.feature-card p {
    color: #374151 !important;
    line-height: 1.7;
    font-size: 1.1rem;
    font-weight: 500;
}

/* Book Details Section */
.book-details-section {
    padding: 100px 0;
    background: var(--brown-light) !important;
    position: relative;
}

.book-content h2 {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    color: #111827 !important;
    margin-bottom: 40px;
    font-weight: 800;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.content-list {
    margin-top: 30px;
}

.content-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 30px;
    padding: 20px;
    background: var(--brown-dark) !important;
    border-radius: var(--border-radius);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    transition: var(--transition);
    border: 2px solid var(--brown-light) !important;
}

.content-item:hover {
    transform: translateX(10px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.12);
}

.content-item i {
    font-size: 2rem;
    color: var(--accent-color);
    margin-right: 20px;
    margin-top: 5px;
}

.content-item h4 {
    font-size: 1.3rem;
    font-weight: 700;
    color: #111827 !important;
    margin-bottom: 8px;
}

.content-item p {
    color: #374151 !important;
    margin: 0;
    font-weight: 500;
}

/* Stats Container */
.stats-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
    margin-top: 40px;
}

.stat-item {
    text-align: center;
    padding: 30px 20px;
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.stat-item:hover {
    transform: scale(1.05);
}

.stat-number {
    font-size: 3rem;
    font-weight: 900;
    color: #2563eb !important;
    display: block;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-label {
    font-size: 1.1rem;
    color: #111827 !important;
    margin-top: 10px;
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        padding: 120px 0 80px;
    }

    .hero-title {
        font-size: 2.75rem;
        line-height: 1.1;
    }

    .hero-subtitle {
        font-size: 1.375rem;
    }

    .hero-description {
        font-size: 1.125rem;
    }

    .section-title {
        font-size: 2.25rem;
    }

    .section-subtitle {
        font-size: 1.125rem;
    }

    .stats-container {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .cta-buttons .btn {
        display: block;
        width: 100%;
        margin-bottom: 15px;
        padding: 16px 24px;
        font-size: 1.1rem;
    }

    .feature-card {
        padding: 32px 24px;
    }

    .testimonial-card {
        padding: 32px 24px;
    }

    .format-card {
        padding: 32px 24px;
    }
}

/* Testimonials Section */
.testimonials-section {
    padding: 100px 0;
    background: var(--brown-medium) !important;
    position: relative;
}

.testimonials-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--brown-medium) !important;
}

#testimonials .section-title,
.testimonials-section .section-title {
    color: #fbbf24 !important;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    font-weight: 800;
}

#testimonials h2.section-title {
    color: #fbbf24 !important;
}

.testimonial-card {
    background: var(--brown-dark) !important;
    padding: 40px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    height: 100%;
    transition: var(--transition);
    border: 2px solid var(--brown-light) !important;
    position: relative;
    overflow: hidden;
}

.testimonial-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, var(--secondary-color), var(--accent-color));
}

.testimonial-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    border-color: var(--secondary-color);
}

/* Enhanced Testimonials Section */
.testimonials-extended-section {
    padding: 100px 0;
    background: var(--brown-light) !important;
    position: relative;
}

.testimonials-extended-section .section-title {
    color: #111827 !important;
    font-weight: 900;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.testimonials-extended-section .section-subtitle {
    color: #374151 !important;
    font-weight: 600;
}

.testimonials-extended-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="testimonial-pattern" width="40" height="40" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1" fill="rgba(52,152,219,0.1)"/><path d="M 15 15 Q 20 10 25 15 Q 20 20 15 15" fill="rgba(52,152,219,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23testimonial-pattern)"/></svg>');
    opacity: 0.3;
    pointer-events: none;
}

/* Testimonial Badges */
.testimonial-badge {
    position: absolute;
    top: -10px;
    right: 15px;
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    color: white;
    z-index: 2;
}

.testimonial-badge {
    background: var(--warning-color);
}

.testimonial-badge.featured-badge {
    background: var(--accent-color);
}

.testimonial-badge.verified-badge {
    background: var(--success-color);
}

.testimonial-badge.international-badge {
    background: var(--secondary-color);
}

.testimonial-badge.academic-badge {
    background: #9b59b6;
}

.testimonial-badge.clinical-badge {
    background: #e67e22;
}

/* Enhanced Testimonial Cards */
.testimonial-card.premium {
    border: 2px solid var(--warning-color);
    background: linear-gradient(135deg, #fff9e6 0%, #ffffff 100%);
}

.testimonial-card.featured {
    border: 2px solid var(--accent-color);
    background: linear-gradient(135deg, #ffe6e6 0%, #ffffff 100%);
}

.testimonial-card.verified {
    border: 2px solid var(--success-color);
    background: linear-gradient(135deg, #e6f7e6 0%, #ffffff 100%);
}

.testimonial-card.international {
    border: 2px solid var(--secondary-color);
    background: linear-gradient(135deg, #e6f3ff 0%, #ffffff 100%);
}

.testimonial-card.academic {
    border: 2px solid #9b59b6;
    background: linear-gradient(135deg, #f3e6ff 0%, #ffffff 100%);
}

.testimonial-card.clinical {
    border: 2px solid #e67e22;
    background: linear-gradient(135deg, #fff0e6 0%, #ffffff 100%);
}

/* Author Location */
.author-location {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-top: 5px;
    font-size: 0.85rem;
    color: var(--text-light);
}

.author-location i {
    color: var(--secondary-color);
    font-size: 0.8rem;
}

/* Enhanced Purchase Popup */
.purchase-popup {
    background: linear-gradient(135deg, var(--success-color), #38a169);
    border-radius: var(--border-radius);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.popup-content {
    padding: 8px 0;
    font-size: 1rem;
    font-weight: 500;
}

.popup-content strong {
    font-weight: 700;
    color: #f7fafc;
}

.testimonial-rating {
    color: var(--warning-color);
    margin-bottom: 20px;
    font-size: 1.1rem;
}

.testimonial-card p {
    font-style: italic;
    color: #111827 !important;
    margin-bottom: 32px;
    line-height: 1.8;
    font-size: 1.125rem;
    font-weight: 500;
}

/* Override for yellow testimonials */
.testimonial-card p[style*="color: #fbbf24"] {
    color: #fbbf24 !important;
    font-weight: 700 !important;
}

.testimonial-author {
    display: flex;
    align-items: center;
}

.author-image {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-right: 15px;
    object-fit: cover;
}

.author-info h5 {
    margin: 0;
    font-weight: 700;
    color: #111827 !important;
}

.author-info span {
    color: #374151 !important;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Preview Section */
.preview-section {
    padding: 80px 0;
    background: var(--light-bg);
}

/* Marketing Videos Section */
.marketing-videos-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: var(--border-radius);
    padding: 60px 40px;
    margin: 40px 0;
    position: relative;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.marketing-videos-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="video-pattern" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="1" fill="rgba(52,152,219,0.1)"/><path d="M 10 10 L 20 15 L 10 20 Z" fill="rgba(52,152,219,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23video-pattern)"/></svg>');
    opacity: 0.3;
    pointer-events: none;
}

.videos-title {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    font-weight: 600;
    color: var(--primary-color);
    text-align: center;
    margin-bottom: 15px;
}

.videos-subtitle {
    font-size: 1.2rem;
    color: var(--text-light);
    text-align: center;
    margin-bottom: 50px;
}

.videos-grid {
    margin-bottom: 50px;
}

/* Video Cards */
.video-card {
    background: var(--white);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    transition: var(--transition);
    position: relative;
    height: 100%;
}

.video-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.video-card.featured {
    border: 2px solid var(--secondary-color);
}

.video-card.premium {
    border: 2px solid var(--warning-color);
}

/* Video Header */
.video-header {
    padding: 25px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    position: relative;
}

.video-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.2);
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 15px;
    backdrop-filter: blur(10px);
}

.premium-badge {
    background: rgba(255, 193, 7, 0.3);
}

.video-header h4 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: white;
}

.video-header p {
    font-size: 1rem;
    opacity: 0.9;
    margin: 0;
    line-height: 1.5;
}

/* Video Wrapper */
.video-wrapper {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    height: 0;
    overflow: hidden;
    background: #000;
}

.responsive-iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
}

.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
    pointer-events: none;
}

.video-card:hover .video-overlay {
    opacity: 1;
}

.play-button {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 2rem;
    transform: scale(0.8);
    transition: var(--transition);
}

.video-card:hover .play-button {
    transform: scale(1);
    background: var(--secondary-color);
    color: white;
}

/* Video Footer */
.video-footer {
    padding: 20px 25px;
    background: var(--light-bg);
}

.video-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.video-stats span {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-light);
    font-size: 0.9rem;
    font-weight: 500;
}

.video-stats i {
    color: var(--secondary-color);
}

/* Video Call-to-Action */
.video-cta {
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
    border-radius: var(--border-radius);
    padding: 40px;
    text-align: center;
    color: white;
    position: relative;
    overflow: hidden;
}

.video-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="medical-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 10 0 L 10 20 M 0 10 L 20 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23medical-pattern)"/></svg>');
    opacity: 0.3;
}

.cta-content {
    position: relative;
    z-index: 2;
}

.video-cta h4 {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: white;
}

.video-cta p {
    font-size: 1.1rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

.video-cta .cta-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.video-cta .btn {
    padding: 15px 30px;
    font-weight: 600;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

/* Decorative Elements */
.video-decorations {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    overflow: hidden;
}

.decoration-element {
    position: absolute;
    color: rgba(52, 152, 219, 0.1);
    font-size: 3rem;
    animation: float 6s ease-in-out infinite;
}

.decoration-1 {
    top: 10%;
    left: 5%;
    animation-delay: 0s;
}

.decoration-2 {
    top: 20%;
    right: 10%;
    animation-delay: 1.5s;
}

.decoration-3 {
    bottom: 30%;
    left: 8%;
    animation-delay: 3s;
}

.decoration-4 {
    bottom: 15%;
    right: 5%;
    animation-delay: 4.5s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    33% {
        transform: translateY(-20px) rotate(5deg);
    }
    66% {
        transform: translateY(10px) rotate(-3deg);
    }
}

/* Professional Gallery */
.professional-gallery {
    padding: 40px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin: 20px 0;
}

.gallery-title {
    font-family: 'Playfair Display', serif;
    font-size: 2.2rem;
    font-weight: 600;
    color: var(--primary-color);
    text-align: center;
    margin-bottom: 10px;
}

.gallery-subtitle {
    font-size: 1.1rem;
    color: var(--text-light);
    text-align: center;
    margin-bottom: 40px;
}

/* Main Gallery Swiper */
.main-gallery-swiper {
    margin-bottom: 20px;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius);
    background: var(--white);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.gallery-image {
    width: 100%;
    height: 500px;
    object-fit: contain;
    background: var(--white);
    transition: var(--transition);
    border-radius: var(--border-radius);
}

.gallery-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 30px 20px 20px;
    transform: translateY(100%);
    transition: var(--transition);
}

.gallery-item:hover .gallery-overlay {
    transform: translateY(0);
}

.gallery-item:hover .gallery-image {
    transform: scale(1.05);
}

.gallery-info h4 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 5px;
    color: white;
}

.gallery-info p {
    font-size: 0.9rem;
    opacity: 0.9;
    margin: 0;
}

/* Thumbnail Gallery */
.thumbnail-gallery-swiper {
    margin-top: 20px;
    padding: 10px 0;
}

.thumbnail-image {
    width: 100%;
    height: 80px;
    object-fit: cover;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
    opacity: 0.7;
    border: 2px solid transparent;
}

.thumbnail-image:hover,
.swiper-slide-thumb-active .thumbnail-image {
    opacity: 1;
    border-color: var(--secondary-color);
    transform: scale(1.05);
}

/* Gallery Navigation */
.gallery-pagination .swiper-pagination-bullet {
    background: var(--secondary-color);
    width: 12px;
    height: 12px;
    opacity: 0.5;
}

.gallery-pagination .swiper-pagination-bullet-active {
    opacity: 1;
    transform: scale(1.2);
}

.gallery-next,
.gallery-prev {
    color: var(--secondary-color);
    background: rgba(255, 255, 255, 0.9);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: var(--transition);
}

.gallery-next:hover,
.gallery-prev:hover {
    background: var(--secondary-color);
    color: white;
    transform: scale(1.1);
}

.gallery-next::after,
.gallery-prev::after {
    font-size: 18px;
    font-weight: 600;
}

/* Gallery Responsive Design */
@media (max-width: 768px) {
    .gallery-image {
        height: 350px;
    }

    .gallery-title {
        font-size: 1.8rem;
    }

    .gallery-subtitle {
        font-size: 1rem;
    }

    .thumbnail-image {
        height: 60px;
    }

    .gallery-next,
    .gallery-prev {
        width: 40px;
        height: 40px;
    }

    .gallery-next::after,
    .gallery-prev::after {
        font-size: 14px;
    }
}

/* Gallery Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.gallery-item {
    animation: zoomIn 0.6s ease;
}

/* Image Modal Styles */
.image-modal {
    backdrop-filter: blur(5px);
}

.modal-image {
    animation: zoomIn 0.4s ease;
}

/* Gallery Loading Animation */
.gallery-image {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

.gallery-image[src] {
    background: none;
    animation: none;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Gallery Hover Effects */
.professional-gallery {
    position: relative;
}

.professional-gallery::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(52, 152, 219, 0.05) 50%, transparent 70%);
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.professional-gallery:hover::before {
    opacity: 1;
}

/* Purchase Section */
.purchase-section {
    padding: 100px 0;
    background: linear-gradient(135deg, var(--brown-darkest) 0%, var(--brown-darker) 100%);
    color: #ffffff !important;
    position: relative;
}

.purchase-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="purchase-pattern" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="1" fill="rgba(255,255,255,0.1)"/><path d="M 10 15 L 15 10 L 20 15 L 15 20 Z" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23purchase-pattern)"/></svg>');
    opacity: 0.3;
}

.purchase-section .section-title {
    color: #ffffff !important;
    text-shadow: 0 4px 6px rgba(0, 0, 0, 0.5);
    font-weight: 900;
}

.purchase-section .section-subtitle {
    color: #f1f5f9 !important;
    font-weight: 600;
}

.discount-banner {
    background: linear-gradient(135deg, var(--accent-color), #c0392b);
    color: white;
    padding: 15px 30px;
    border-radius: 50px;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
    margin-bottom: 40px;
    animation: pulse 2s infinite;
}

.format-card {
    background: var(--brown-dark) !important;
    border-radius: var(--border-radius);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.4);
    padding: 40px;
    text-align: center;
    height: 100%;
    transition: var(--transition);
    position: relative;
    border: 2px solid var(--brown-light) !important;
    overflow: hidden;
}

.format-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg, var(--secondary-color), var(--accent-color));
}

.format-card:hover {
    transform: translateY(-12px);
    box-shadow: 0 32px 64px -12px rgba(0, 0, 0, 0.35);
    border-color: var(--secondary-color);
}

.format-card.popular {
    border-color: var(--warning-color);
    transform: scale(1.05);
}

.popular-badge {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--warning-color);
    color: white;
    padding: 8px 20px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
}

.format-header {
    margin-bottom: 30px;
}

.format-header i {
    font-size: 3rem;
    color: var(--secondary-color);
    margin-bottom: 15px;
}

.format-header h3 {
    font-size: 1.8rem;
    font-weight: 800;
    color: #111827 !important;
    margin-bottom: 15px;
}

.price {
    margin-bottom: 20px;
}

.original-price {
    text-decoration: line-through;
    color: var(--text-light);
    font-size: 1.1rem;
    margin-right: 10px;
}

.sale-price {
    font-size: 2rem;
    font-weight: 700;
    color: var(--accent-color);
}

.format-features {
    list-style: none;
    padding: 0;
    margin-bottom: 30px;
}

.format-features li {
    padding: 8px 0;
    color: #111827 !important;
    font-weight: 500;
}

.format-features i {
    color: var(--success-color);
    margin-right: 10px;
}

.purchase-buttons .btn {
    width: 100%;
    margin-bottom: 10px;
    padding: 12px;
    font-weight: 600;
    border-radius: var(--border-radius);
}

.btn-block {
    display: block;
    width: 100%;
}

.guarantee-text {
    color: var(--text-light);
    font-size: 1rem;
    margin-top: 30px;
}

.guarantee-text i {
    color: var(--success-color);
    margin-right: 8px;
}

/* All Stores Section */
.all-stores-section {
    background: var(--brown-medium) !important;
    border-radius: var(--border-radius);
    padding: 40px;
    margin: 20px 0;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.stores-title {
    font-family: 'Playfair Display', serif;
    font-size: 1.8rem;
    font-weight: 800;
    color: #111827 !important;
    margin-bottom: 30px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stores-grid {
    margin-top: 20px;
}

.store-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px 15px;
    background: var(--brown-dark) !important;
    border-radius: var(--border-radius);
    text-decoration: none;
    color: #ffffff !important;
    transition: var(--transition);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    height: 100px;
    border: 2px solid var(--brown-light) !important;
    font-weight: 700;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.store-link:hover {
    transform: translateY(-5px);
    color: white;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.store-link i {
    font-size: 2rem;
    margin-bottom: 8px;
}

.store-link span {
    font-size: 0.9rem;
    font-weight: 600;
}

/* Store Brand Colors */
.store-link.paypal:hover {
    background: #0070ba;
    border-color: #0070ba;
}

.store-link.dratef:hover {
    background: var(--secondary-color);
    border-color: var(--secondary-color);
}

.store-link.amazon:hover {
    background: #ff9900;
    border-color: #ff9900;
}

.store-link.apple:hover {
    background: #000000;
    border-color: #000000;
}

.store-link.kobo:hover {
    background: #68b3e2;
    border-color: #68b3e2;
}

.store-link.bn:hover {
    background: #2e7d32;
    border-color: #2e7d32;
}

/* FAQ Section */
.faq-section {
    padding: 100px 0;
    background: var(--brown-medium) !important;
    position: relative;
}

.faq-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--brown-medium) !important;
}

.accordion-item {
    border: none;
    margin-bottom: 15px;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.accordion-button {
    background: var(--brown-dark) !important;
    border: 2px solid var(--brown-light) !important;
    font-weight: 900;
    color: #ffffff !important;
    padding: 24px 32px;
    font-size: 1.125rem;
    border-radius: var(--border-radius) !important;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    box-shadow: none !important;
}

.accordion-button:not(.collapsed) {
    background: var(--brown-light) !important;
    color: #ffffff !important;
    border-color: var(--brown-light) !important;
    box-shadow: none !important;
}

.accordion-button:focus {
    border-color: var(--brown-light) !important;
    box-shadow: 0 0 0 0.25rem rgba(210, 105, 30, 0.25) !important;
}

.accordion-button::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23ffffff'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e") !important;
}

.accordion-button:not(.collapsed)::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23ffffff'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e") !important;
    transform: rotate(-180deg) !important;
}

.accordion-button:not(.collapsed) {
    background: var(--secondary-color);
    color: white;
    box-shadow: none;
}

.accordion-button:focus {
    box-shadow: none;
    border: none;
}

.accordion-item {
    background: transparent !important;
    border: none !important;
    margin-bottom: 20px !important;
}

.accordion-body {
    background: var(--brown-light) !important;
    padding: 32px;
    color: #ffffff !important;
    line-height: 1.8;
    font-size: 1.1rem;
    font-weight: 700;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    border: 2px solid var(--brown-light) !important;
    border-top: none !important;
}

.accordion-collapse {
    border: none !important;
    transition: all 0.3s ease !important;
}

.accordion-collapse.show {
    display: block !important;
    opacity: 1 !important;
}

.accordion-collapse:not(.show) {
    display: none !important;
    opacity: 0 !important;
}

.accordion-header {
    margin-bottom: 0 !important;
}

/* Ensure FAQ section is visible */
.faq-section {
    position: relative !important;
    z-index: 1 !important;
}

.faq-section .accordion {
    position: relative !important;
    z-index: 2 !important;
}



/* More Books Section */
.more-books-section {
    padding: 60px 0;
    background: var(--white);
}

/* Social Share Section */
.social-share-section {
    padding: 60px 0;
    background: var(--light-bg);
}

.social-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.social-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition);
    color: white;
}

.social-btn.twitter {
    background: #1da1f2;
}

.social-btn.facebook {
    background: #4267b2;
}

.social-btn.linkedin {
    background: #0077b5;
}

.social-btn.whatsapp {
    background: #25d366;
}

.social-btn:hover {
    transform: translateY(-3px);
    color: white;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.hashtags {
    background: var(--white);
    padding: 20px;
    border-radius: var(--border-radius);
    margin-top: 20px;
}

.hashtags p {
    margin: 0;
    color: var(--text-light);
    font-size: 0.9rem;
    line-height: 1.6;
}

/* Follow Dr. Atef Ahmed Section */
.follow-doctor-section {
    padding: 100px 0;
    background: linear-gradient(135deg, var(--brown-darkest) 0%, var(--brown-darker) 50%, var(--brown-dark) 100%);
    color: #ffffff !important;
    position: relative;
}

.follow-doctor-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="social-pattern" width="35" height="35" patternUnits="userSpaceOnUse"><circle cx="17.5" cy="17.5" r="1.5" fill="rgba(255,255,255,0.1)"/><path d="M 12 17.5 L 17.5 12 L 23 17.5 L 17.5 23 Z" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23social-pattern)"/></svg>');
    opacity: 0.3;
}

.follow-doctor-section .section-title {
    color: #ffffff !important;
    font-weight: 900;
    text-shadow: 0 4px 6px rgba(0, 0, 0, 0.5);
}

.follow-doctor-section .section-subtitle {
    color: #f1f5f9 !important;
    font-weight: 600;
}

.social-media-grid {
    margin-top: 40px;
}

.social-media-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px 15px;
    background: var(--brown-dark) !important;
    border-radius: var(--border-radius);
    text-decoration: none;
    color: #ffffff !important;
    transition: var(--transition);
    backdrop-filter: blur(10px);
    border: 2px solid var(--brown-light) !important;
    height: 100px;
    min-height: 100px;
    font-weight: 700;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.social-media-card:hover {
    transform: translateY(-5px);
    color: white;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.social-media-card i {
    font-size: 2rem;
    margin-bottom: 8px;
}

.social-media-card span {
    font-size: 0.9rem;
    font-weight: 500;
}

/* Social Media Brand Colors */
.social-media-card.instagram:hover {
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
}

.social-media-card.facebook:hover {
    background: #4267B2;
}

.social-media-card.youtube:hover {
    background: #FF0000;
}

.social-media-card.twitter:hover {
    background: #000000;
}

.social-media-card.threads:hover {
    background: #000000;
}

.social-media-card.tiktok:hover {
    background: #000000;
}

.social-media-card.linkedin:hover {
    background: #0077B5;
}

.social-media-card.telegram:hover {
    background: #0088cc;
}

.social-media-card.facebook-group:hover {
    background: #42b883;
}

.social-media-card.whatsapp:hover {
    background: #25D366;
}

/* Footer */
.footer {
    background: var(--brown-darkest) !important;
    color: #ffffff !important;
    padding: 60px 0 20px;
}

.footer-brand h4 {
    color: #ffffff !important;
    font-weight: 800;
    margin-bottom: 15px;
}

.footer-brand p {
    color: #e5e7eb !important;
    margin-bottom: 20px;
    line-height: 1.6;
    font-weight: 500;
}

.footer-social {
    display: flex;
    gap: 15px;
}

.footer-social a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--secondary-color);
    color: white;
    border-radius: 50%;
    text-decoration: none;
    transition: var(--transition);
}

.footer-social a:hover {
    background: var(--accent-color);
    transform: translateY(-3px);
}

.footer h5 {
    color: #ffffff !important;
    font-weight: 700;
    margin-bottom: 20px;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 10px;
}

.footer-links a {
    color: #e5e7eb !important;
    text-decoration: none;
    transition: var(--transition);
    font-weight: 500;
}

.footer-links a:hover {
    color: var(--secondary-color);
}

.footer-divider {
    border-color: #34495e;
    margin: 40px 0 20px;
}

.footer-copyright {
    color: #e5e7eb !important;
    margin: 0;
    font-size: 0.9rem;
    font-weight: 500;
}

.footer-copyright a {
    color: var(--secondary-color);
    text-decoration: none;
}

.footer-copyright a:hover {
    text-decoration: underline;
}

/* Additional Responsive Styles */
@media (max-width: 992px) {
    .stats-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .social-buttons {
        justify-content: center;
    }

    .social-btn {
        font-size: 0.9rem;
        padding: 10px 15px;
    }
}

@media (max-width: 576px) {
    .hero-section {
        padding: 100px 0 60px;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .format-card.popular {
        transform: none;
    }

    .stats-container {
        grid-template-columns: 1fr;
    }

    .social-buttons {
        flex-direction: column;
        align-items: center;
    }

    .social-btn {
        width: 200px;
        justify-content: center;
    }

    .purchase-popup {
        right: 10px;
        left: 10px;
        max-width: none;
    }

    .social-media-card {
        height: 80px;
        min-height: 80px;
        padding: 15px 10px;
    }

    .social-media-card i {
        font-size: 1.5rem;
        margin-bottom: 5px;
    }

    .social-media-card span {
        font-size: 0.8rem;
    }

    /* Video Section Mobile Styles */
    .marketing-videos-section {
        padding: 40px 20px;
        margin: 20px 0;
    }

    .videos-title {
        font-size: 2rem;
    }

    .videos-subtitle {
        font-size: 1rem;
    }

    .video-header {
        padding: 20px;
    }

    .video-header h4 {
        font-size: 1.3rem;
    }

    .video-cta {
        padding: 30px 20px;
    }

    .video-cta h4 {
        font-size: 1.5rem;
    }

    .video-cta .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .video-cta .btn {
        width: 100%;
        max-width: 280px;
        margin-bottom: 15px;
    }

    .decoration-element {
        font-size: 2rem;
    }

    .play-button {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}

/* Accessibility Improvements */
.btn:focus,
.form-control:focus,
.accordion-button:focus {
    outline: 2px solid var(--secondary-color);
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .sticky-header,
    .purchase-popup,
    .social-share-section {
        display: none;
    }

    .hero-section,
    .about-section,
    .testimonials-section {
        page-break-inside: avoid;
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #000000;
        --secondary-color: #0066cc;
        --text-dark: #000000;
        --text-light: #333333;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* BROWN BACKGROUND WITH BOLD WHITE TEXT - UNIVERSAL OVERRIDE */

/* ALL TEXT ELEMENTS - BOLD WHITE */
*,
*::before,
*::after,
h1, h2, h3, h4, h5, h6,
p, span, div, a, li, td, th,
.section-title,
.section-subtitle,
.hero-title,
.hero-subtitle,
.hero-description,
.feature-card h3,
.feature-card p,
.content-item h4,
.content-item p,
.testimonial-author h5,
.testimonial-author span,
.format-header h3,
.format-features li,
.stores-title,
.videos-title,
.gallery-title,
.stat-number,
.stat-label,
.accordion-button,
.accordion-body,
.buyers-count,
.nav-link,
.navbar-brand,
.footer-brand h4,
.footer-brand p,
.footer h5,
.footer-links a,
.footer-copyright,
.store-link,
.store-link span,
.author-location,
.testimonial-rating,
.price,
.original-price,
.sale-price {
    color: #ffffff !important;
    font-weight: 700 !important;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8) !important;
}

/* EXTRA BOLD FOR HEADINGS */
h1, h2, h3, h4, h5, h6,
.section-title,
.hero-title {
    font-weight: 900 !important;
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9) !important;
}

/* KEEP YELLOW TESTIMONIALS YELLOW BUT BOLD */
.testimonial-card p[style*="color: #fbbf24"] {
    color: #fbbf24 !important;
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9) !important;
    font-weight: 900 !important;
}

/* YELLOW SECTION TITLE */
section#testimonials .section-title,
section.testimonials-section .section-title {
    color: #fbbf24 !important;
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9) !important;
    font-weight: 900 !important;
}

/* BUTTONS - WHITE TEXT */
.btn {
    color: #ffffff !important;
    font-weight: 800 !important;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5) !important;
}

/* STATISTICS - WHITE TEXT */
.stat-number {
    color: #ffffff !important;
    font-weight: 900 !important;
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9) !important;
}

/* SOCIAL MEDIA CARDS - WHITE TEXT */
.social-media-card,
.social-media-card span {
    color: #ffffff !important;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8) !important;
    font-weight: 700 !important;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --light-bg: #1a1a1a;
        --white: #2d2d2d;
        --text-dark: #ffffff;
        --text-light: #cccccc;
    }

    .hero-section {
        background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    }

    .sticky-header {
        background: rgba(45, 45, 45, 0.95);
    }
}
